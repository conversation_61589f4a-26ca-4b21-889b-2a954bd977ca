[project]
name = "ducklake-speedrun"
version = "0.1.0"
description = "Duck Lake ETL utility for incremental event processing with local DuckDB storage and Parquet files"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "duckdb>=1.3",  # Using stable version that supports ducklake
    "dlt[duckdb]>=1.0",
    "openai>=1.14.0",
    "python-dotenv>=1.0",
]

[project.scripts]
events-pipeline = "events_pipeline:main"
insert-event-data = "insert_event_data_loop:main"

[dependency-groups]
dev = [
    "pytest>=8.4.0",
]
