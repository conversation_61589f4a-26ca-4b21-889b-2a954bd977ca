# Duck Lake Speedrun

uv run events_pipeline.py --run --data-path ./ducklake-data/events

A Python ETL utility for creating and managing Duck Lake catalogs with local DuckDB storage and Parquet data files.

## Features

- Create and manage Duck Lake catalogs with local storage
- Incremental ETL pipeline for event data
- Stage data from DuckDB source to DuckDB staging
- Promote staged tables to Duck Lake with local Parquet files
- Generate text embeddings using OpenAI API
- Complete local operation with no cloud dependencies
- Native JSON column support for complex data types

## Events Pipeline

The project includes a specialized incremental ETL pipeline for event data with native JSON support.

### Inserting Test Event Data

```bash
# Insert 100 random events (default)
uv run insert_event_data.py

# Insert 500 events
uv run insert_event_data.py --rows 500

# Truncate table and insert fresh data
uv run insert_event_data.py --truncate --rows 1000

# Events are automatically generated with realistic distributions
```

### Running the Events Pipeline

```bash
# Run complete incremental ETL pipeline
uv run events_pipeline.py --run --data-path ./ducklake-data/events

# Extract only (to staging)
uv run events_pipeline.py --extract-only

# Promote only (from staging to Duck Lake)
uv run events_pipeline.py --promote-only --data-path ./ducklake-data/events
```

### Querying JSON Data

Duck Lake now supports native JSON columns. The events table includes a `details` JSON column that can be queried using DuckDB's JSON functions:

```python
# Example: Extract specific fields from JSON
SELECT 
    event_timestamp,
    json_extract_string(details, '$.user_id') as user_id,
    json_extract_string(details, '$.login_method') as login_method
FROM events
WHERE event_type = 'user_login'

# Example: Filter by JSON field values
SELECT * FROM events
WHERE json_extract_string(details, '$.status') = 'failed'

# Example: Aggregate by JSON fields
SELECT 
    json_extract_string(details, '$.endpoint') as endpoint,
    COUNT(*) as request_count
FROM events
WHERE event_type = 'api_request'
GROUP BY endpoint
```

See `query_events_json.py` for more comprehensive examples of JSON querying.

## Prerequisites

- Python ≥3.10
- Local file system for data storage
- OpenAI API key (for embeddings, optional)

## Installation

This project uses [uv](https://docs.astral.sh/uv) for dependency management.

1. Clone the repository:
```bash
git clone <your-repo-url>
cd ducklake-speedrun
```

2. Install dependencies with uv:
```bash
uv sync
```

This will create a virtual environment and install all required packages.

## Configuration

Create a `.env` file in the project root with your credentials (optional):

```env
# OpenAI API key (optional, for embeddings)
OPENAI_API_KEY=sk-your-openai-api-key
```

The pipeline works entirely with local DuckDB databases and local file storage, so no external database or cloud credentials are required.

## Architecture

The pipeline operates entirely with local storage:

1. **Source Database**: DuckDB database containing source event data
2. **Staging Database**: DuckDB database for incremental data processing
3. **Duck Lake Catalog**: DuckDB metadata database with local Parquet data files
4. **Incremental Processing**: Tracks timestamps to process only new events
5. **Local Storage**: All data stored in local directories and DuckDB files

## Troubleshooting

### Common Issues

- **DuckDB Extension Loading**: The pipeline automatically installs the ducklake extension from core_nightly
- **File Permissions**: Ensure the process has read/write access to the data directory
- **Disk Space**: Monitor available disk space for DuckDB files and Parquet data
- **Memory Usage**: Large datasets may require adjusting DuckDB memory settings